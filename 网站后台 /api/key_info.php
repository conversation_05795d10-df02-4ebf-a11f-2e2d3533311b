<?php
/**
 * API密钥信息接口
 * 
 * 处理API密钥状态检查和更新通知
 * 确保客户端能够及时获取最新的API配置
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, X-API-Version, X-Request-Timestamp, X-Request-Signature');

require_once 'api_base.php';

class KeyInfoApi extends ApiBase {
    
    /**
     * 处理API密钥信息请求
     */
    public function handleKeyInfo() {
        // 检查请求方法
        if ($this->method !== 'POST') {
            return $this->respondError('Method not allowed', 405);
        }
        
        // 获取请求数据
        $key = $this->request['key'] ?? '';
        $currentApiKey = $this->request['current_api_key'] ?? '';
        $timestamp = $this->request['timestamp'] ?? 0;
        $signature = $this->request['signature'] ?? '';
        
        // 验证必要参数
        if (empty($key)) {
            return $this->respondError('Missing required parameters', 400);
        }
        
        // 验证时间戳
        if (!$this->validateTimestamp($timestamp)) {
            return $this->respondError('Invalid timestamp', 400, 'INVALID_TIMESTAMP');
        }
        
        // 验证签名
        if (!empty($signature) && !$this->verifySignature($this->request, $timestamp, $signature)) {
            return $this->respondError('Invalid signature', 400, 'INVALID_SIGNATURE');
        }
        
        try {
            // 【修复】验证卡密 - 使用BINARY进行严格的大小写敏感比较
            $stmt = $this->db->prepare("
                SELECT lk.*
                FROM license_keys lk
                WHERE BINARY lk.key_value = ? AND lk.status = 'active' AND lk.expiry_date > NOW()
            ");
            $stmt->execute([$key]);
            $keyData = $stmt->fetch();
            
            if (!$keyData) {
                return $this->respondError('Invalid license key', 403, 'INVALID_KEY');
            }
            
            // 获取所有店铺信息（包括主店铺和额外店铺）
            $stores = [];
            
            // 添加主店铺
            if (!empty($keyData['store_name']) || !empty($keyData['wechat_store_id'])) {
                $stores[] = [
                    'store_name' => $keyData['store_name'],
                    'wechat_store_id' => $keyData['wechat_store_id']
                ];
            }
            
            // 如果是多店铺卡密，获取额外的店铺信息
            if ($keyData['is_multi_store']) {
                try {
                    $stores_stmt = $this->db->prepare("SELECT store_name, wechat_store_id FROM license_key_stores WHERE license_key_id = ? ORDER BY id ASC");
                    $stores_stmt->execute([$keyData['id']]);
                    $additional_stores = $stores_stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                    if (!empty($additional_stores)) {
                        $stores = array_merge($stores, $additional_stores);
                    }
                } catch (Exception $e) {
                    error_log("获取额外店铺信息失败: " . $e->getMessage());
                }
            }
            
            // 获取当前API配置
            $currentApiConfig = $this->getCurrentApiConfig();
            
            // 检查API密钥是否有更新
            $apiKeyUpdated = false;
            if ($currentApiKey && $currentApiKey !== $currentApiConfig['api_key']) {
                $apiKeyUpdated = true;
            }
            
            // 检查脚本是否有更新
            $scriptUpdated = false;
            $lastScriptCheck = $this->getLastScriptCheck($keyData['id']);
            if ($lastScriptCheck && $keyData['script_updated_at'] > $lastScriptCheck) {
                $scriptUpdated = true;
            }
            
            // 更新最后检查时间
            $this->updateLastScriptCheck($keyData['id']);
            
            // 记录API密钥信息检查日志
            $this->logKeyInfoCheck($keyData['id']);
            
            // 准备响应数据
            $responseData = [
                'success' => true,
                'message' => 'API密钥信息获取成功',
                'api_key' => $currentApiConfig['api_key'],
                'api_key_updated' => $apiKeyUpdated,
                'script_updated' => $scriptUpdated,
                'api_config' => [
                    'servers' => [
                        'primary' => $currentApiConfig['primary_server'] ?? 'https://xiaomeihuakefu.cn',
                        'backup' => $currentApiConfig['backup_server'] ?? 'https://api.xiaomeihuakefu.cn',
                        'secure' => $currentApiConfig['secure_server'] ?? 'https://secure.xiaomeihuakefu.cn'
                    ],
                    'version' => $currentApiConfig['version'] ?? 'v2'
                ],
                'security_token' => bin2hex(random_bytes(16)),
                'key_info' => [
                    'expiry_date' => $keyData['expiry_date'] ?? $keyData['expires_at'] ?? null,
                    'type' => $keyData['type'] ?? 'day',
                    'store_name' => $keyData['store_name'] ?? '',
                    'wechat_store_id' => $keyData['wechat_store_id'] ?? '',
                    'stores' => $stores,
                    'is_multi_store' => (bool)$keyData['is_multi_store'],
                    'has_customer_service' => (bool)($keyData['has_customer_service'] ?? 1),
                    'has_product_listing' => (bool)($keyData['has_product_listing'] ?? 0)
                ]
            ];
            
            // 如果脚本有更新，包含新脚本代码
            if ($scriptUpdated && !empty($keyData['script_code'])) {
                $responseData['script'] = $keyData['script_code'];
            }
            
            return $this->respondSuccess($responseData);
            
        } catch (PDOException $e) {
            error_log("API密钥信息检查失败: " . $e->getMessage());
            return $this->respondError('Server error', 500);
        }
    }
    
    /**
     * 获取当前API配置
     */
    private function getCurrentApiConfig() {
        try {
            // 从系统设置表获取API配置
            $stmt = $this->db->prepare("
                SELECT setting_key, setting_value 
                FROM system_settings 
                WHERE setting_key IN ('api_secret_key', 'api_version', 'primary_server', 'backup_server', 'secure_server')
            ");
            $stmt->execute();
            $settings = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
            
            return [
                'api_key' => $settings['api_secret_key'] ?? 'default_key',
                'version' => $settings['api_version'] ?? 'v2',
                'primary_server' => $settings['primary_server'] ?? 'https://xiaomeihuakefu.cn',
                'backup_server' => $settings['backup_server'] ?? 'https://api.xiaomeihuakefu.cn',
                'secure_server' => $settings['secure_server'] ?? 'https://secure.xiaomeihuakefu.cn'
            ];
            
        } catch (PDOException $e) {
            error_log("获取API配置失败: " . $e->getMessage());
            return [
                'api_key' => 'default_key',
                'version' => 'v2',
                'primary_server' => 'https://xiaomeihuakefu.cn',
                'backup_server' => 'https://api.xiaomeihuakefu.cn',
                'secure_server' => 'https://secure.xiaomeihuakefu.cn'
            ];
        }
    }
    
    /**
     * 获取最后脚本检查时间
     */
    private function getLastScriptCheck($keyId) {
        try {
            $stmt = $this->db->prepare("
                SELECT last_script_check 
                FROM license_keys 
                WHERE id = ?
            ");
            $stmt->execute([$keyId]);
            $result = $stmt->fetch();
            
            return $result ? $result['last_script_check'] : null;
            
        } catch (PDOException $e) {
            error_log("获取最后脚本检查时间失败: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 更新最后脚本检查时间
     */
    private function updateLastScriptCheck($keyId) {
        try {
            $stmt = $this->db->prepare("
                UPDATE license_keys 
                SET last_script_check = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$keyId]);
            
        } catch (PDOException $e) {
            error_log("更新最后脚本检查时间失败: " . $e->getMessage());
        }
    }
    
    /**
     * 记录API密钥信息检查日志
     */
    private function logKeyInfoCheck($keyId) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO key_info_checks 
                (key_id, ip_address, user_agent, created_at) 
                VALUES (?, ?, ?, NOW())
            ");
            $stmt->execute([
                $keyId,
                $_SERVER['REMOTE_ADDR'],
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
            
        } catch (PDOException $e) {
            error_log("记录API密钥信息检查日志失败: " . $e->getMessage());
        }
    }
}

// 创建API实例并处理请求
$api = new KeyInfoApi();
$api->handleKeyInfo();